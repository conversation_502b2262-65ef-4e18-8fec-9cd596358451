/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2025-05-09 10:00:00
 * @LastEditors: zhangzao
 * @LastEditTime: 2025-06-16 14:34:22
 * @Description: 数据标准目录管理接口
 */
import { http } from "@/utils/http";

/** 标准目录项 */
export interface DataStandardItem {
  id: number;
  name: string;
  englishName: string;
  description?: string;
  parentId?: number;
  layeringType?: number;
  children?: DataStandardItem[];
  // 其他字段按实际接口补充
}

/** 分页响应 */
export interface DataStandardPageResponse {
  total: number;
  list: DataStandardItem[];
}

/**
 * 获取标准目录分页列表
 * @param params 查询参数
 */
export const getDataStandardPage = (params: {
  name?: string;
  englishName?: string;
  layeringTypes?: number[];
  page: number;
  limit: number;
}) => {
  return http.request<DataStandardPageResponse>(
    "get",
    "/data-integrate/data-standard/dir/page",
    { params }
  );
};

/** 新建或编辑目录 */
export const saveDataStandard = (data: {
  name: string;
  englishName: string;
  englishShortName: string;
  comment: string;
  layeringType: number;
  pid: number;
}) => {
  return http.request<any>("post", "/data-integrate/data-standard/dir/save", {
    data
  });
};

/** 获取目录详情 */
export const getDataStandardDetail = (id: number) => {
  return http.request<any>("get", `/data-integrate/data-standard/dir/${id}`);
};

/** 更新目录 */
export const updateDataStandard = (data: {
  id: number;
  name: string;
  englishName: string;
  englishShortName: string;
  comment: string;
  layeringType: number;
  pid: number;
}) => {
  return http.request<any>("post", "/data-integrate/data-standard/dir/update", {
    data
  });
};

/** 批量删除目录 */
export const deleteDataStandard = (ids: number[]) => {
  return http.request<any>("delete", "/data-integrate/data-standard/dir", {
    data: ids
  });
};

// ==================== 字段标准相关接口 ====================

/** 字段标准项 */
export interface FieldStandardItem {
  id: number;
  dirId: number; // 所属目录ID
  cnName: string; // 中文名称
  enName: string; // 英文名称
  enAbbr?: string; // 英文简称
  dataType: string; // 数据类型
  length?: number; // 长度
  precision?: number; // 精度
  required: boolean; // 是否必填
  defaultValue?: string; // 默认值
  standardRef?: string; // 引用标准
  businessDef?: string; // 业务定义
  createTime?: number;
  updateTime?: number;
}

/** 字段标准分页响应 */
export interface FieldStandardPageResponse {
  total: number;
  list: FieldStandardItem[];
}

/**
 * 获取字段标准分页列表
 * @param params 查询参数
 */
export const getFieldStandardPage = (params: {
  dirId?: number; // 目录ID
  cnName?: string; // 中文名称
  dataType?: string; // 数据类型
  page: number;
  limit: number;
}) => {
  return http.request<FieldStandardPageResponse>(
    "get",
    "/data-integrate/data-standard/field/page",
    { params }
  );
};

/**
 * 新增字段标准
 * @param data 字段标准数据
 */
export const saveFieldStandard = (data: {
  dirId: number;
  cnName: string;
  enName: string;
  enAbbr?: string;
  dataType: string;
  length?: number;
  precision?: number;
  required: boolean;
  defaultValue?: string;
  standardRef?: string;
  businessDef?: string;
}) => {
  return http.request<any>("post", "/data-integrate/data-standard/field/save", {
    data
  });
};

/**
 * 获取字段标准详情
 * @param id 字段标准ID
 */
export const getFieldStandardDetail = (id: number) => {
  return http.request<FieldStandardItem>("get", `/data-integrate/data-standard/field/${id}`);
};

/**
 * 更新字段标准
 * @param data 字段标准数据
 */
export const updateFieldStandard = (data: {
  id: number;
  dirId: number;
  cnName: string;
  enName: string;
  enAbbr?: string;
  dataType: string;
  length?: number;
  precision?: number;
  required: boolean;
  defaultValue?: string;
  standardRef?: string;
  businessDef?: string;
}) => {
  return http.request<any>("post", "/data-integrate/data-standard/field/update", {
    data
  });
};

/**
 * 批量删除字段标准
 * @param ids 字段标准ID数组
 */
export const deleteFieldStandard = (ids: number[]) => {
  return http.request<any>("delete", "/data-integrate/data-standard/field", {
    data: ids
  });
};

// ==================== 数仓分层相关接口 ====================

/** 数仓分层项 */
export interface LayeringTypeItem {
  id: number;
  name: string;
  shortName: string;
  description?: string;
}

/**
 * 获取数仓分层列表
 */
export const getLayeringTypeList = () => {
  return http.request<LayeringTypeItem[]>(
    "get",
    "/data-integrate/data-warehouse/layering-type/list"
  );
};
